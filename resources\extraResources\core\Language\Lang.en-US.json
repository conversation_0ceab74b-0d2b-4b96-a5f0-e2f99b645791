{"Test": "Test Us", "Custom": "{0}", "File_404": "File not found", "File_Del": "Failed to delete file", "ReadMediaInfoError": "No audio track found for this file", "Network_RequestFail": "Network request failed", "UnknownError": "Unknown error", "TaskCore_AiModel_404": "Ai model not found, please restart the task", "TaskInfo_ExtractAudio": "Extracting audio file", "TaskInfo_ExtractAudioError": "Extract audio file error", "TaskInfo_Stop": "Stop task", "TaskInfo_ExtractSubtitle": "Extracting subtitle", "TaskInfo_ExtractSubtitleError": "Extract subtitle error: {0}", "TaskInfo_ExtractSubtitleComplete": "Extract subtitle completed, time: {0}", "TaskInfo_Voice_SplitFile": "Splitting file {0}", "TaskInfo_Voice_Ing": "Voice separation in progress, {0}%, speed {1}", "TaskInfo_Voic_Error": "Failure in voice separation", "TaskInfo_Voic_ErrorMerge": "Voice separation merge file error", "TaskInfo_Translation_Ing": "Translation in progress", "TaskInfo_Translation_ExtractMore": "Waiting to extract more subtitles", "TaskInfo_Translation_ErrorCount": "There are {0} subtitle errors, please retry", "TaskInfo_Translation_SaveError": "Save subtitle file error, please try manually", "TaskInfo_Alignment_Start": "Please be patient as the timeline optimization process may take some time", "TaskInfo_Alignment_Ing": "Timing optimization in progress {0}", "TaskInfo_Alignment_Error": "Exception during timing optimization", "TaskInfo_Alignment_Error_Result": "Timing optimization failed"}