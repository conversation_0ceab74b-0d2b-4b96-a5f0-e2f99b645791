{"Test": "Test De", "Custom": "{0}", "File_404": "Datei nicht gefunden", "File_Del": "Datei löschen fehlgeschlagen", "ReadMediaInfoError": "Keine Audiospur für diese Datei gefunden", "Network_RequestFail": "Netzwerkanfrage fehlgeschlagen", "UnknownError": "Unbekannter Fehler", "TaskCore_AiModel_404": "Ai-<PERSON>l nicht gefunden", "TaskInfo_ExtractAudio": "Audio-<PERSON><PERSON>n", "TaskInfo_ExtractAudioError": "Audio-<PERSON><PERSON>", "TaskInfo_Stop": "Aufgabe stoppen", "TaskInfo_ExtractSubtitle": "Untertitel extrahieren", "TaskInfo_ExtractSubtitleError": "<PERSON><PERSON><PERSON><PERSON>: {0}", "TaskInfo_ExtractSubtitleComplete": "extrahieren abgeschlossen, Zeit: {0}", "TaskInfo_Voice_SplitFile": "Datei {0} teilen", "TaskInfo_Voice_Ing": "Stimmtrennung in Arbeit, {0}%,  {1}", "TaskInfo_Voic_Error": "Stimmtrennung fehlgeschlagen", "TaskInfo_Voic_ErrorMerge": "Stimmtrennung Zusammenführungsdatei Fehler", "TaskInfo_Translation_Ing": "Übersetzung in Arbeit", "TaskInfo_Translation_ExtractMore": "<PERSON>ten auf mehr Untertitel extrahieren", "TaskInfo_Translation_ErrorCount": "<PERSON><PERSON> gibt {0} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bitte versuchen Si<PERSON> es erneut", "TaskInfo_Translation_SaveError": "Untertitel-<PERSON><PERSON>, bitte versuchen Si<PERSON> manuell", "TaskInfo_Alignment_Start": "<PERSON>te haben <PERSON>uld, da die Zeitachseoptimierung etwas Zeit in Anspruch nimmt", "TaskInfo_Alignment_Ing": "Optimierung des Timings läuft {0}", "TaskInfo_Alignment_Error": "Ausnahme während der Optimierung des Timings", "TaskInfo_Alignment_Error_Result": "Optimierung des Timings fehlgeschlagen"}