fail: 2025-08-11 20:15:30.6778007 +08:00 星期一 L System.Logging.StringLogging[0] #17
      task2      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Engine.Application.Model.TaskException: 剩余时长不足，请付费. Error Id:dbf491a4ef984682a37b37d3369e6f50
         at Engine.Application.Taskmgr.TaskRunBase.StartAsync() in D:\code\subtitle\EngineNew\Engine\Engine.Application\Taskmgr\TaskRunBase.cs:line 89
         at Engine.Application.Taskmgr.TaskWork.ExecuteAsync(CancellationToken stoppingToken) in D:\code\subtitle\EngineNew\Engine\Engine.Application\Taskmgr\TaskWork.cs:line 107
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-11 20:15:37.1200009 +08:00 星期一 L System.Logging.StringLogging[0] #17
      task2      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Engine.Application.Model.TaskException: 剩余时长不足，请付费. Error Id:e4150d66ca6e46bcbf3f89bbea665218
         at Engine.Application.Taskmgr.TaskRunBase.StartAsync() in D:\code\subtitle\EngineNew\Engine\Engine.Application\Taskmgr\TaskRunBase.cs:line 89
         at Engine.Application.Taskmgr.TaskWork.ExecuteAsync(CancellationToken stoppingToken) in D:\code\subtitle\EngineNew\Engine\Engine.Application\Taskmgr\TaskWork.cs:line 107
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-11 20:15:48.0365211 +08:00 星期一 L System.Logging.StringLogging[0] #17
      task2      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Engine.Application.Model.TaskException: 剩余时长不足，请付费. Error Id:d855d3994cb74edba4affb165245ab6a
         at Engine.Application.Taskmgr.TaskRunBase.StartAsync() in D:\code\subtitle\EngineNew\Engine\Engine.Application\Taskmgr\TaskRunBase.cs:line 89
         at Engine.Application.Taskmgr.TaskWork.ExecuteAsync(CancellationToken stoppingToken) in D:\code\subtitle\EngineNew\Engine\Engine.Application\Taskmgr\TaskWork.cs:line 107
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-11 21:30:23.1679810 +08:00 星期一 L System.Logging.StringLogging[0] #83
      task2      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Engine.Application.Model.TaskException: 剩余时长不足，请付费. Error Id:0500ec94f88b464788d2edab58a54d4f
         at Engine.Application.Taskmgr.TaskRunBase.StartAsync() in D:\code\subtitle\EngineNew\Engine\Engine.Application\Taskmgr\TaskRunBase.cs:line 89
         at Engine.Application.Taskmgr.TaskWork.ExecuteAsync(CancellationToken stoppingToken) in D:\code\subtitle\EngineNew\Engine\Engine.Application\Taskmgr\TaskWork.cs:line 107
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
